#include <iostream>
#include <cassert>
#include <string>
#include <memory>
#include <functional>
#include <vector>

#include "property.h"

#define TEST_VERIFY(condition) \
    do { \
        if (!(condition)) { \
            std::cerr << "TEST FAILED: " << #condition << " at line " << __LINE__ << std::endl; \
            assert(false); \
        } else { \
            std::cout << "PASS: " << #condition << std::endl; \
        } \
    } while(0)

#define TEST_COMPARE(actual, expected) \
    do { \
        if ((actual) != (expected)) { \
            std::cerr << "TEST FAILED: " << #actual << " != " << #expected \
                      << " (actual: " << (actual) << ", expected: " << (expected) \
                      << ") at line " << __LINE__ << std::endl; \
            assert(false); \
        } else { \
            std::cout << "PASS: " << #actual << " == " << #expected << std::endl; \
        } \
    } while(0)

struct DtorCounter {
    static int counter;
    bool shouldIncrement = false;
    ~DtorCounter() { if (shouldIncrement) ++counter; }
};

int DtorCounter::counter = 0;

void testFunctorBinding() {
    std::cout << "\n=== Testing Functor Binding ===" << std::endl;
    
    Property<int> property([]() { return 42; });
    TEST_COMPARE(property.value(), 42);
    
    property.setBinding([]() { return 100; });
    TEST_COMPARE(property.value(), 100);
    
    property.setBinding([]() { return 50; });
    TEST_COMPARE(property.value(), 50);
}

void testBasicDependencies() {
    std::cout << "\n=== Testing Basic Dependencies ===" << std::endl;
    
    Property<int> right(100);
    Property<int> left(makePropertyBinding([&right]() { return right.value(); }));
    
    TEST_COMPARE(left.value(), 100);
    
    right = 42;
    TEST_COMPARE(left.value(), 42);
}

void testMultipleDependencies() {
    std::cout << "\n=== Testing Multiple Dependencies ===" << std::endl;
    
    Property<int> firstDependency(1);
    Property<int> secondDependency(2);
    
    Property<int> sum;
    sum.setBinding([&]() { return firstDependency.value() + secondDependency.value(); });
    
    TEST_COMPARE(sum.value(), 3);
    
    firstDependency = 10;
    TEST_COMPARE(sum.value(), 12);
    
    secondDependency = 20;
    TEST_COMPARE(sum.value(), 30);
    
    firstDependency = 1;
    secondDependency = 1;
    TEST_COMPARE(sum.value(), 2);
}

void testRecursiveDependency() {
    std::cout << "\n=== Testing Recursive Dependency ===" << std::endl;
    
    Property<int> first(1);
    
    Property<int> second;
    second.setBinding(makePropertyBinding([&first]() { return first.value(); }));
    
    Property<int> third;
    third.setBinding(makePropertyBinding([&second]() { return second.value(); }));
    
    TEST_COMPARE(third.value(), 1);
    
    first = 2;
    TEST_COMPARE(third.value(), 2);
}

void testBoolProperty() {
    std::cout << "\n=== Testing Bool Property ===" << std::endl;
    
    Property<bool> first(true);
    Property<bool> second(false);
    Property<bool> all([&]() { return first.value() && second.value(); });
    
    TEST_COMPARE(all.value(), false);
    
    second = true;
    TEST_COMPARE(all.value(), true);
}

void testTakeBinding() {
    std::cout << "\n=== Testing Take Binding ===" << std::endl;
    
    PropertyBinding<int> existingBinding;
    TEST_VERIFY(existingBinding.isNull());
    
    Property<int> first(100);
    Property<int> second(makePropertyBinding([&first]() { return first.value(); }));
    
    TEST_COMPARE(second.value(), 100);
    
    existingBinding = second.takeBinding();
    TEST_VERIFY(!existingBinding.isNull());
    
    first = 10;
    TEST_COMPARE(second.value(), 100);
    
    second = 25;
    TEST_COMPARE(second.value(), 25);
    
    second.setBinding(existingBinding);
    TEST_COMPARE(second.value(), 10);
    TEST_VERIFY(!existingBinding.isNull());
}

void testReplaceBinding() {
    std::cout << "\n=== Testing Replace Binding ===" << std::endl;
    
    Property<int> first(100);
    Property<int> second(makePropertyBinding([&first]() { return first.value(); }));
    
    TEST_COMPARE(second.value(), 100);
    
    auto constantBinding = makePropertyBinding([]() { return 42; });
    auto oldBinding = second.setBinding(constantBinding);
    TEST_COMPARE(second.value(), 42);
    
    second.setBinding(oldBinding);
    TEST_COMPARE(second.value(), 100);
}

void testSettingValueRemovesBinding() {
    std::cout << "\n=== Testing Setting Value Removes Binding ===" << std::endl;
    
    Property<int> source(42);
    Property<int> property(makePropertyBinding([&source]() { return source.value(); }));
    
    TEST_COMPARE(property.value(), 42);
    TEST_VERIFY(!property.binding().isNull());
    
    property = 100;
    TEST_COMPARE(property.value(), 100);
    TEST_VERIFY(property.binding().isNull());
    
    source = 1;
    TEST_COMPARE(property.value(), 100);
    TEST_VERIFY(property.binding().isNull());
}

void testSetBindingFunctor() {
    std::cout << "\n=== Testing Set Binding Functor ===" << std::endl;
    
    Property<int> property;
    Property<int> injectedValue(100);
    
    property.setBinding([&injectedValue]() { return injectedValue.value(); });
    injectedValue = 200;
    TEST_COMPARE(property.value(), 200);
}

void testChangeHandler() {
    std::cout << "\n=== Testing Change Handler ===" << std::endl;
    
    Property<int> testProperty(0);
    std::vector<int> recordedValues;
    
    {
        auto handler = testProperty.onValueChanged([&]() {
            recordedValues.push_back(testProperty.value());
        });
        
        testProperty = 1;
        testProperty = 2;
    }
    testProperty = 3;
    
    TEST_COMPARE(recordedValues.size(), 2u);
    TEST_COMPARE(recordedValues[0], 1);
    TEST_COMPARE(recordedValues[1], 2);
}

void testSubscribe() {
    std::cout << "\n=== Testing Subscribe ===" << std::endl;
    
    Property<int> testProperty(42);
    std::vector<int> recordedValues;
    
    {
        auto handler = testProperty.subscribe([&]() {
            recordedValues.push_back(testProperty.value());
        });
        
        testProperty = 1;
        testProperty = 2;
    }
    testProperty = 3;
    
    TEST_COMPARE(recordedValues.size(), 3u);
    TEST_COMPARE(recordedValues[0], 42);
    TEST_COMPARE(recordedValues[1], 1);
    TEST_COMPARE(recordedValues[2], 2);
}

void testBindingFunctionDtorCalled() {
    std::cout << "\n=== Testing Binding Function Destructor ===" << std::endl;
    
    DtorCounter::counter = 0;
    DtorCounter dc;
    {
        Property<int> prop;
        prop.setBinding([dc]() mutable {
            dc.shouldIncrement = true;
            return 42;
        });
        TEST_COMPARE(prop.value(), 42);
    }
    TEST_COMPARE(DtorCounter::counter, 1);
}

int main() {
    std::cout << "Starting simplified property binding system tests..." << std::endl;
    
    testFunctorBinding();
    testBasicDependencies();
    testMultipleDependencies();
    testRecursiveDependency();
    testBoolProperty();
    testTakeBinding();
    testReplaceBinding();
    testSettingValueRemovesBinding();
    testSetBindingFunctor();
    testChangeHandler();
    testSubscribe();
    testBindingFunctionDtorCalled();
    
    std::cout << "\nAll tests completed!" << std::endl;
    return 0;
}
