# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: property
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = E$:\Projects\Code\AI$ Agent\property\build\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug\
# =============================================================================
# Object build statements for EXECUTABLE target property


#############################################
# Order-only phony target for property

build cmake_object_order_depends_target_property: phony || .

build CMakeFiles\property.dir\property.cpp.obj: CXX_COMPILER__property_unscanned_Debug E$:\Projects\Code\AI$ Agent\property\property.cpp || cmake_object_order_depends_target_property
  FLAGS = -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd
  INCLUDES = -I"E:\Projects\Code\AI Agent\property" -I"E:\Projects\Code\AI Agent\property\global" -I"E:\Projects\Code\AI Agent\property\global\types" -I"E:\Projects\Code\AI Agent\property\utils"
  OBJECT_DIR = CMakeFiles\property.dir
  OBJECT_FILE_DIR = CMakeFiles\property.dir
  TARGET_COMPILE_PDB = CMakeFiles\property.dir\
  TARGET_PDB = property.pdb

build CMakeFiles\property.dir\main.cpp.obj: CXX_COMPILER__property_unscanned_Debug E$:\Projects\Code\AI$ Agent\property\main.cpp || cmake_object_order_depends_target_property
  FLAGS = -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd
  INCLUDES = -I"E:\Projects\Code\AI Agent\property" -I"E:\Projects\Code\AI Agent\property\global" -I"E:\Projects\Code\AI Agent\property\global\types" -I"E:\Projects\Code\AI Agent\property\utils"
  OBJECT_DIR = CMakeFiles\property.dir
  OBJECT_FILE_DIR = CMakeFiles\property.dir
  TARGET_COMPILE_PDB = CMakeFiles\property.dir\
  TARGET_PDB = property.pdb


# =============================================================================
# Link build statements for EXECUTABLE target property


#############################################
# Link the executable property.exe

build property.exe: CXX_EXECUTABLE_LINKER__property_Debug CMakeFiles\property.dir\property.cpp.obj CMakeFiles\property.dir\main.cpp.obj
  FLAGS = -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  OBJECT_DIR = CMakeFiles\property.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\property.dir\
  TARGET_FILE = property.exe
  TARGET_IMPLIB = property.lib
  TARGET_PDB = property.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D "E:\Projects\Code\AI Agent\property\build\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug" && C:\Qt\Tools\CMake_64\bin\cmake-gui.exe -S"E:\Projects\Code\AI Agent\property" -B"E:\Projects\Code\AI Agent\property\build\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug""
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D "E:\Projects\Code\AI Agent\property\build\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug" && C:\Qt\Tools\CMake_64\bin\cmake.exe --regenerate-during-build -S"E:\Projects\Code\AI Agent\property" -B"E:\Projects\Code\AI Agent\property\build\Desktop_Qt_6_9_0_MSVC2022_64bit-Debug""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util

# =============================================================================
# Target aliases.

build property: phony property.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: E:/Projects/Code/AI Agent/property/build/Desktop_Qt_6_9_0_MSVC2022_64bit-Debug

build all: phony property.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | .qtc\package-manager\auto-setup.cmake .qtc\package-manager\maintenance_tool_provider.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.30.5\CMakeCCompiler.cmake CMakeFiles\3.30.5\CMakeCXXCompiler.cmake CMakeFiles\3.30.5\CMakeRCCompiler.cmake CMakeFiles\3.30.5\CMakeSystem.cmake E$:\Projects\Code\AI$ Agent\property\CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build .qtc\package-manager\auto-setup.cmake .qtc\package-manager\maintenance_tool_provider.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake C$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\3.30.5\CMakeCCompiler.cmake CMakeFiles\3.30.5\CMakeCXXCompiler.cmake CMakeFiles\3.30.5\CMakeRCCompiler.cmake CMakeFiles\3.30.5\CMakeSystem.cmake E$:\Projects\Code\AI$ Agent\property\CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
